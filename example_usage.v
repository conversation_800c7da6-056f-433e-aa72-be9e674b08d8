`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date: 2025/01/23
// Design Name: 
// Module Name: example_usage
// Project Name: 
// Target Devices: 
// Tool Versions: 
// Description: 8b/10b LVDS传输器的使用示例
//              演示如何在实际项目中集成和使用传输器
// 
// Dependencies: lvds_8b10b_transmitter.v
// 
// Revision:
// Revision 0.01 - File Created
// Additional Comments:
// 
//////////////////////////////////////////////////////////////////////////////////

module example_usage (
    // 系统接口
    input  wire        clk,           // 系统时钟
    input  wire        rst_n,         // 复位信号
    
    // 用户数据接口
    input  wire        start_tx,      // 开始传输信号
    input  wire [7:0]  user_data,     // 用户数据
    input  wire        data_valid,    // 数据有效信号
    output wire        tx_busy,       // 传输忙信号
    
    // LVDS输出
    output wire        lvds_p,        // LVDS正信号
    output wire        lvds_n         // LVDS负信号
);

// 内部信号定义
wire [7:0] tx_data;
wire       tx_valid;
wire       tx_ready;
wire       lvds_data;

// 用户接口控制逻辑
reg        tx_enable;
reg [7:0]  data_buffer;

// 传输控制状态机
localparam IDLE = 1'b0,
           BUSY = 1'b1;

reg tx_state;

// 传输状态控制
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        tx_state <= IDLE;
        tx_enable <= 1'b0;
        data_buffer <= 8'h00;
    end else begin
        case (tx_state)
            IDLE: begin
                if (start_tx && data_valid) begin
                    // 开始传输，锁存数据
                    data_buffer <= user_data;
                    tx_enable <= 1'b1;
                    tx_state <= BUSY;
                end
            end
            
            BUSY: begin
                if (tx_valid && tx_ready) begin
                    // 数据已被传输器接收，返回空闲状态
                    tx_enable <= 1'b0;
                    tx_state <= IDLE;
                end
            end
        endcase
    end
end

// 输出信号分配
assign tx_data  = data_buffer;
assign tx_valid = tx_enable;
assign tx_busy  = (tx_state == BUSY);

// 实例化8b/10b LVDS传输器
lvds_8b10b_transmitter u_transmitter (
    .clk(clk),
    .rst_n(rst_n),
    .s_axis_tdata(tx_data),
    .s_axis_tvalid(tx_valid),
    .s_axis_tready(tx_ready),
    .lvds_data(lvds_data)
);

// LVDS差分输出驱动
// 注意：在实际FPGA中，应该使用专用的LVDS输出原语
// 这里仅作为示例
assign lvds_p = lvds_data;
assign lvds_n = ~lvds_data;

endmodule

//////////////////////////////////////////////////////////////////////////////////
// 高级使用示例：带FIFO缓存的连续数据传输
//////////////////////////////////////////////////////////////////////////////////

module advanced_usage_with_fifo (
    // 系统接口
    input  wire        clk,           // 系统时钟
    input  wire        rst_n,         // 复位信号
    
    // 数据输入接口（支持突发传输）
    input  wire [7:0]  data_in,       // 输入数据
    input  wire        data_wr_en,    // 数据写使能
    output wire        fifo_full,     // FIFO满信号
    output wire        fifo_empty,    // FIFO空信号
    
    // 控制接口
    input  wire        tx_enable,     // 传输使能
    output wire        tx_active,     // 传输活动指示
    
    // LVDS输出
    output wire        lvds_data      // LVDS数据输出
);

// FIFO相关信号
wire [7:0] fifo_data_out;
wire       fifo_rd_en;
wire       fifo_valid;

// 传输控制
reg        auto_tx_enable;
wire       tx_ready;

// 自动传输控制逻辑
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        auto_tx_enable <= 1'b0;
    end else begin
        if (tx_enable && !fifo_empty) begin
            auto_tx_enable <= 1'b1;
        end else if (fifo_empty && tx_ready) begin
            auto_tx_enable <= 1'b0;
        end
    end
end

// FIFO读控制
assign fifo_rd_en = auto_tx_enable && tx_ready && !fifo_empty;
assign tx_active = auto_tx_enable;

// 简化的FIFO实现（实际项目中建议使用IP核）
reg [7:0] fifo_mem [0:15];  // 16深度FIFO
reg [3:0] wr_ptr, rd_ptr;
reg [4:0] fifo_count;

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        wr_ptr <= 4'b0;
        rd_ptr <= 4'b0;
        fifo_count <= 5'b0;
    end else begin
        // 写操作
        if (data_wr_en && !fifo_full) begin
            fifo_mem[wr_ptr] <= data_in;
            wr_ptr <= wr_ptr + 1'b1;
        end
        
        // 读操作
        if (fifo_rd_en && !fifo_empty) begin
            rd_ptr <= rd_ptr + 1'b1;
        end
        
        // 计数器更新
        case ({data_wr_en && !fifo_full, fifo_rd_en && !fifo_empty})
            2'b10: fifo_count <= fifo_count + 1'b1;  // 只写
            2'b01: fifo_count <= fifo_count - 1'b1;  // 只读
            default: ; // 同时读写或都不操作，计数不变
        endcase
    end
end

assign fifo_data_out = fifo_mem[rd_ptr];
assign fifo_valid = !fifo_empty;
assign fifo_full = (fifo_count == 5'd16);
assign fifo_empty = (fifo_count == 5'd0);

// 实例化8b/10b LVDS传输器
lvds_8b10b_transmitter u_transmitter (
    .clk(clk),
    .rst_n(rst_n),
    .s_axis_tdata(fifo_data_out),
    .s_axis_tvalid(fifo_valid && auto_tx_enable),
    .s_axis_tready(tx_ready),
    .lvds_data(lvds_data)
);

endmodule
