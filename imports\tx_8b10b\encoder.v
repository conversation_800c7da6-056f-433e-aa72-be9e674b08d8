`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date:    16:38:45 10/11/2013 
// Design Name:    yanwei
// Module Name:    encoder 
// Project Name:   8b/10b������
// Target Devices: spartan 6
// Tool versions:  13.2
// Description: 
//
// Dependencies: 
//
// Revision: 
// Revision 0.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module encoder (data_in,rd_in,clk,rst,data_out,rd_out_i,en_data_in
    );
input  [7:0]  data_in;
input  en_data_in;
input  [1:0]  rd_in;
input  clk;
input  rst;
//output  [1:0]  rd_out;
output  [9:0]  data_out;

 output wire  [1:0]  rd_out_i;
 wire  [1:0]  rd_out_5_o;
 reg   [1:0]  rd_out;

 reg    [9:0] data_out;
 reg    [5:0] data_5b_out_o;
 wire    [5:0] data_5b_out_i;
 reg    [2:0] data_3b_i;
 wire   [3:0] data_3b_o;
reg [1:0]rd_out_5_o_r;
encoder_5b ed_5b (.data_in({data_in[4:0]}),
                  .en_data_in(en_data_in),
                  .clk(clk),
						.rst(rst),
						.sel_in({data_3b_o[1:0]}),
						.rd_in(rd_in),
						.rd_out(rd_out_5_o),
						.data_out(data_5b_out_i));

encoder_3b ed_3b (.data_in({data_3b_i}),
                  .en_data_in(en_data_in),
                  .clk(clk),
						.rst(rst),
						.sel_in({data_5b_out_i[2:0]}),
						.rd_in(rd_out_5_o_r),
						.rd_out(rd_out_i),
						.data_out(data_3b_o));

always @(posedge clk)	begin
	   if(!rst)	begin
	   data_out <= 10'b0;
	   rd_out   <= 2'b0;
		rd_out_5_o_r<=2'b0;
	   end
	   else	begin
	   data_out <= {data_5b_out_o,data_3b_o};
	   rd_out   <= rd_out_i;
		rd_out_5_o_r<=rd_out_5_o;
	   end
end

always @(posedge clk)	begin
       data_5b_out_o <= data_5b_out_i;
		 data_3b_i  <= {data_in[7:5]};
		 end


endmodule


module encoder_5b (rd_in,data_in,sel_in,clk,rst,data_out,rd_out,en_data_in
    );
input  [4:0] data_in;
input  en_data_in;
input  clk;
input  rst;
input  [1:0]  sel_in;
input  [1:0]  rd_in;
output  [1:0]  rd_out;
output  [5:0]  data_out;

 reg  [5:0]  data_out;
 reg  [1:0]  rd_out;


always @(posedge clk)  begin
	   if(!rst)	begin
	   data_out <= 6'b0;
	   rd_out <= 2'b0;
	   end 
	   else if(en_data_in) begin
      if(data_in!=5'b00111) begin
		if(rd_in==2'b00) begin
			case ({data_in[4:0]})
			5'b00000 : begin data_out <= 6'b100111;rd_out <= 2'b01; end
			5'b00001 : begin data_out <= 6'b011101;rd_out <= 2'b01; end
			5'b00010 : begin data_out <= 6'b101101;rd_out <= 2'b01; end
			5'b00011 : begin data_out <= 6'b110001;rd_out <= 2'b00; end
			5'b00100 : begin data_out <= 6'b110101;rd_out <= 2'b01; end
			5'b00101 : begin data_out <= 6'b101001;rd_out <= 2'b00; end
			5'b00110 : begin data_out <= 6'b011001;rd_out <= 2'b00; end
			5'b01000 : begin data_out <= 6'b111001;rd_out <= 2'b01; end
			5'b01001 : begin data_out <= 6'b100101;rd_out <= 2'b00; end
			5'b01010 : begin data_out <= 6'b010101;rd_out <= 2'b00; end
			5'b01011 : begin data_out <= 6'b110100;rd_out <= 2'b00; end
			5'b01100 : begin data_out <= 6'b001101;rd_out <= 2'b00; end
			5'b01101 : begin data_out <= 6'b101100;rd_out <= 2'b00; end
			5'b01110 : begin data_out <= 6'b011100;rd_out <= 2'b00; end
			5'b01111 : begin data_out <= 6'b010111;rd_out <= 2'b01; end
			5'b10000 : begin data_out <= 6'b011011;rd_out <= 2'b01; end
			5'b10001 : begin data_out <= 6'b100011;rd_out <= 2'b00; end
			5'b10010 : begin data_out <= 6'b010011;rd_out <= 2'b00; end
			5'b10011 : begin data_out <= 6'b110010;rd_out <= 2'b00; end
			5'b10100 : begin data_out <= 6'b001011;rd_out <= 2'b00; end
			5'b10101 : begin data_out <= 6'b101010;rd_out <= 2'b00; end
			5'b10110 : begin data_out <= 6'b011010;rd_out <= 2'b00; end
			5'b10111 : begin data_out <= 6'b111010;rd_out <= 2'b01; end
			5'b11000 : begin data_out <= 6'b110011;rd_out <= 2'b01; end
			5'b11001 : begin data_out <= 6'b100110;rd_out <= 2'b00; end
			5'b11010 : begin data_out <= 6'b010110;rd_out <= 2'b00; end
			5'b11011 : begin data_out <= 6'b110110;rd_out <= 2'b01; end
			5'b11100 : begin data_out <= 6'b001110;rd_out <= 2'b00; end
			5'b11101 : begin data_out <= 6'b101110;rd_out <= 2'b01; end
			5'b11110 : begin data_out <= 6'b011110;rd_out <= 2'b01; end
			5'b11111 : begin data_out <= 6'b101011;rd_out <= 2'b01; end
			endcase
		end
		else if(rd_in==2'b01) begin
			case ({data_in[4:0]})
			5'b00000 : begin data_out <= 6'b011000;rd_out <= 2'b00; end
			5'b00001 : begin data_out <= 6'b100010;rd_out <= 2'b00; end
			5'b00010 : begin data_out <= 6'b010010;rd_out <= 2'b00; end
			5'b00011 : begin data_out <= 6'b110001;rd_out <= 2'b01; end
			5'b00100 : begin data_out <= 6'b001010;rd_out <= 2'b00; end
			5'b00101 : begin data_out <= 6'b101001;rd_out <= 2'b01; end
			5'b00110 : begin data_out <= 6'b011001;rd_out <= 2'b01; end
			5'b01000 : begin data_out <= 6'b000110;rd_out <= 2'b00; end
			5'b01001 : begin data_out <= 6'b100101;rd_out <= 2'b01; end
			5'b01010 : begin data_out <= 6'b010101;rd_out <= 2'b01; end
			5'b01011 : begin data_out <= 6'b110100;rd_out <= 2'b01; end
			5'b01100 : begin data_out <= 6'b001101;rd_out <= 2'b01; end
			5'b01101 : begin data_out <= 6'b101100;rd_out <= 2'b01; end
			5'b01110 : begin data_out <= 6'b011100;rd_out <= 2'b01; end
			5'b01111 : begin data_out <= 6'b101000;rd_out <= 2'b00; end
			5'b10000 : begin data_out <= 6'b100100;rd_out <= 2'b00; end
			5'b10001 : begin data_out <= 6'b100011;rd_out <= 2'b01; end
			5'b10010 : begin data_out <= 6'b010011;rd_out <= 2'b01; end
			5'b10011 : begin data_out <= 6'b110010;rd_out <= 2'b01; end
			5'b10100 : begin data_out <= 6'b001011;rd_out <= 2'b01; end
			5'b10101 : begin data_out <= 6'b101010;rd_out <= 2'b01; end
			5'b10110 : begin data_out <= 6'b011010;rd_out <= 2'b01; end
			5'b10111 : begin data_out <= 6'b000101;rd_out <= 2'b00; end
			5'b11000 : begin data_out <= 6'b001100;rd_out <= 2'b00; end
			5'b11001 : begin data_out <= 6'b100110;rd_out <= 2'b01; end
			5'b11010 : begin data_out <= 6'b010110;rd_out <= 2'b01; end
			5'b11011 : begin data_out <= 6'b001001;rd_out <= 2'b00; end
			5'b11100 : begin data_out <= 6'b001110;rd_out <= 2'b01; end
			5'b11101 : begin data_out <= 6'b010001;rd_out <= 2'b00; end
			5'b11110 : begin data_out <= 6'b100001;rd_out <= 2'b00; end
			5'b11111 : begin data_out <= 6'b010100;rd_out <= 2'b00; end
			endcase
		end
		else begin
			  case ({data_in[4:0]})
			5'b00000 : begin data_out <= 6'b100111;rd_out <= 2'b00; end
			5'b00001 : begin data_out <= 6'b011101;rd_out <= 2'b00; end
			5'b00010 : begin data_out <= 6'b101101;rd_out <= 2'b00; end
			5'b00011 : begin data_out <= 6'b110001;rd_out <= 2'b11; end
			5'b00100 : begin data_out <= 6'b110101;rd_out <= 2'b00; end			
			5'b00101 : begin data_out <= 6'b101001;rd_out <= 2'b11; end			
			5'b00110 : begin data_out <= 6'b011001;rd_out <= 2'b11; end			
			5'b01000 : begin data_out <= 6'b111001;rd_out <= 2'b00; end			
			5'b01001 : begin data_out <= 6'b100101;rd_out <= 2'b11; end			
			5'b01010 : begin data_out <= 6'b010101;rd_out <= 2'b11; end			
			5'b01011 : begin data_out <= 6'b110100;rd_out <= 2'b11; end			
			5'b01100 : begin data_out <= 6'b001101;rd_out <= 2'b11; end			
			5'b01101 : begin data_out <= 6'b101100;rd_out <= 2'b11; end			
			5'b01110 : begin data_out <= 6'b011100;rd_out <= 2'b11; end			
			5'b01111 : begin data_out <= 6'b010111;rd_out <= 2'b00; end			
			5'b10000 : begin data_out <= 6'b011011;rd_out <= 2'b00; end			
			5'b10001 : begin data_out <= 6'b100011;rd_out <= 2'b11; end			
			5'b10010 : begin data_out <= 6'b010011;rd_out <= 2'b11; end			
			5'b10011 : begin data_out <= 6'b110010;rd_out <= 2'b11; end			
			5'b10100 : begin data_out <= 6'b001011;rd_out <= 2'b11; end			
			5'b10101 : begin data_out <= 6'b101010;rd_out <= 2'b11; end			
			5'b10110 : begin data_out <= 6'b011010;rd_out <= 2'b11; end			
			5'b10111 : begin data_out <= 6'b111010;rd_out <= 2'b01; end			
			5'b11000 : begin data_out <= 6'b110011;rd_out <= 2'b00; end			
			5'b11001 : begin data_out <= 6'b100110;rd_out <= 2'b11; end			
			5'b11010 : begin data_out <= 6'b010110;rd_out <= 2'b11; end			
			5'b11011 : begin data_out <= 6'b110110;rd_out <= 2'b00; end			
			5'b11100 : begin data_out <= 6'b001110;rd_out <= 2'b11; end			
			5'b11101 : begin data_out <= 6'b101110;rd_out <= 2'b00; end			
			5'b11110 : begin data_out <= 6'b011110;rd_out <= 2'b00; end
			5'b11111 : begin data_out <= 6'b101011;rd_out <= 2'b00; end
			endcase
		end
	end
		  else if(data_in == 5'b00111) begin
				case(sel_in)
				2'b00 : data_out <= 6'b111000;
				default : data_out <= 6'b000111;
				endcase
				rd_out <= rd_in;
		      end
//			else if(data_in == 5'b10111) begin
//				case(sel_in)
//				2'b00 : if(rd_in=='b00) begin data_out <= 6'b111010; rd_out <= 'b01; end
//							else if(rd_in=='b01) begin data_out <= 6'b111010; rd_out <= 'b01; end		//1��������
//							else if(rd_in=='b11) begin data_out <= 6'b111010; rd_out <= 'b00; end
//				default : if(rd_in=='b00) begin data_out <= 6'b000101;  rd_out <= 'b11; end
//							 else if(rd_in=='b01) begin data_out <= 6'b000101;  rd_out <= 'b00; end
//							 else if(rd_in=='b11) begin data_out <= 6'b000101;  rd_out <= 'b11; end		//0��������
//				endcase	
//		      end
   end  else begin
	         data_out <= data_out;
	         rd_out <= rd_out;
             end	
end
						

endmodule 



module encoder_3b (rd_in,data_in,sel_in,clk,rst,data_out,rd_out,en_data_in);
input  [2:0]  data_in;
input  en_data_in;
input  [1:0]  rd_in;
input  [2:0]  sel_in;
input  clk;
input  rst;
output  [3:0]  data_out;
output  [1:0]  rd_out;

 reg  [1:0] rd_out;
 reg  [3:0] data_out;


always @(posedge clk)  begin
	   if(!rst)	begin
	   data_out <= 4'b0;
	  rd_out <= 2'b0;
	   end
	   else if(en_data_in) begin
		if((data_in!=3'b011)&&(data_in!=3'b111)&&(data_in!=3'b100)) begin
			if(rd_in==2'b00) begin
			case ({data_in[2:0]})
			3'b000 : begin data_out <= 4'b1011;rd_out <= 2'b01; end
			3'b001 : begin data_out <= 4'b1001;rd_out <= 2'b00; end
			3'b010 : begin data_out <= 4'b0101;rd_out <= 2'b00; end
			//3'b100 : begin data_out <= 4'b1101;rd_out <= 2'b01; end
			3'b101 : begin data_out <= 4'b1010;rd_out <= 2'b00; end
			3'b110 : begin data_out <= 4'b0110;rd_out <= 2'b00; end		
			endcase
		   end
			else if(rd_in==2'b01) begin
			case ({data_in[2:0]})
			3'b000 : begin data_out <= 4'b0100;rd_out <= 2'b00; end
			3'b001 : begin data_out <= 4'b1001;rd_out <= 2'b01; end
			3'b010 : begin data_out <= 4'b0101;rd_out <= 2'b01; end
			//3'b100 : begin data_out <= 4'b0010;rd_out <= 2'b00; end
			3'b101 : begin data_out <= 4'b1010;rd_out <= 2'b01; end
			3'b110 : begin data_out <= 4'b0110;rd_out <= 2'b01; end	
			endcase
		   end
		   else if(rd_in==2'b11) begin
			case ({data_in[2:0]})
			3'b000 : begin data_out <= 4'b1011;rd_out <= 2'b01; end
			3'b001 : begin data_out <= 4'b1001;rd_out <= 2'b11; end
			3'b010 : begin data_out <= 4'b0101;rd_out <= 2'b11; end
			//3'b100 : begin data_out <= 4'b1101;rd_out <= 2'b01; end
			3'b101 : begin data_out <= 4'b1010;rd_out <= 2'b11; end
			3'b110 : begin data_out <= 4'b0110;rd_out <= 2'b11; end
			endcase	
		   end
	   end
		else if(data_in==3'b111) begin
				if(rd_in==2'b00) begin
				case({sel_in[1:0]})
				2'b11 : begin data_out <= 4'b0111;rd_out <= 2'b01; end
				default: begin data_out <= 4'b1110;rd_out <= 2'b01; end
				endcase
			   end
				
				if(rd_in==2'b01) begin
				case({sel_in[1:0]})
				2'b00 : begin data_out <= 4'b1000;rd_out <= 2'b00; end
				default: begin data_out <= 4'b0001;rd_out <= 2'b00; end
				endcase
			   end
				
				if(rd_in==2'b11) begin
				case({sel_in[1:0]})
				2'b11 : begin data_out <= 4'b0111;rd_out <= 2'b01; end
				default: begin data_out <= 4'b1110;rd_out <= 2'b01; end
				endcase
			   end
	   end
		else if(data_in==3'b100) begin
				case(sel_in) 
				3'b000 :  begin 
							 if(rd_in=='b00) begin data_out <= 4'b1101; rd_out <= 'b01; end
							 else if(rd_in=='b01) begin data_out <= 4'b1101; rd_out <= 'b01; end
							 else if(rd_in=='b11) begin data_out <= 4'b1101; rd_out <= 'b00; end 
							 end
													
				default :  begin 
							 if(rd_in=='b00) begin data_out <= 4'b0010; rd_out <= 'b11; end
							 else if(rd_in=='b01) begin data_out <= 4'b0010; rd_out <= 'b00; end
							 else if(rd_in=='b11) begin data_out <= 4'b0010; rd_out <= 'b11; end
							 end
				endcase
		end
		else if(data_in==3'b011) begin
				case(sel_in) 
				3'b111 : begin data_out <= 4'b0011;rd_out <= 'b01; end
				default : begin  data_out <= 4'b1100;rd_out <= 'b00; end
				endcase
		end
		
		
   end
		else begin
          data_out <= data_out;
	       rd_out <= rd_out;	
           end     			 
end
		


endmodule 




