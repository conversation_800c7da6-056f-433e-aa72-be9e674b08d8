`timescale 1ns / 1ps

module lvds_8b10b_transmitter (
    // 时钟和复位
    input  wire        clk,           // 系统时钟
    input  wire        rst_n,         // 复位信号（低电平有效）
    
    // AXI Stream 输入接口
    input  wire [7:0]  s_axis_tdata,  // 输入数据
    input  wire        s_axis_tvalid, // 输入数据有效
    output reg         s_axis_tready, // 准备接收数据
    
    // LVDS 串行输出
    output reg         lvds_data      // 单bit串行数据输出
);

// 状态机定义
localparam [1:0] IDLE     = 2'b00,  // 空闲状态，等待输入数据
                 ENCODE   = 2'b01,  // 编码状态，等待编码器输出
                 TRANSMIT = 2'b10;  // 传输状态，串行输出数据

// 内部信号定义
reg [1:0]  current_state;           // 当前状态
reg [1:0]  next_state;              // 下一状态

// 编码器相关信号
reg [7:0]  encode_data;             // 送入编码器的数据
reg        encode_enable;           // 编码器使能信号
reg [1:0]  rd_state;                // 运行差异状态
wire [9:0] encoded_data;            // 编码器输出的10位数据
wire [1:0] rd_out;                  // 编码器输出的运行差异

// 并串转换相关信号
reg [9:0]  shift_register;          // 10位移位寄存器
reg [3:0]  bit_counter;             // 位计数器 (0-9)

// 实例化8b/10b编码器
encoder u_encoder (
    .data_in(encode_data),           // 8位输入数据
    .rd_in(rd_state),                // 运行差异输入
    .clk(clk),                       // 时钟
    .rst(rst_n),                     // 复位
    .data_out(encoded_data),         // 10位编码输出
    .rd_out_i(rd_out),               // 运行差异输出
    .en_data_in(encode_enable)       // 编码使能
);

// 状态机 - 组合逻辑
always @(*) begin
    case (current_state)
        IDLE: begin
            // 空闲状态：等待有效数据输入
            if (s_axis_tvalid && s_axis_tready) begin
                next_state = ENCODE;
            end else begin
                next_state = IDLE;
            end
        end
        
        ENCODE: begin
            // 编码状态：等待编码器完成（1个时钟周期）
            next_state = TRANSMIT;
        end
        
        TRANSMIT: begin
            // 传输状态：串行输出10位数据
            if (bit_counter == 4'd9) begin
                next_state = IDLE;
            end else begin
                next_state = TRANSMIT;
            end
        end
        
        default: begin
            next_state = IDLE;
        end
    endcase
end

// 状态机 - 时序逻辑
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        current_state <= IDLE;
    end else begin
        current_state <= next_state;
    end
end

// AXI Stream 握手控制
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        s_axis_tready <= 1'b0;
    end else begin
        // 只有在空闲状态才能接收新数据
        s_axis_tready <= (current_state == IDLE);
    end
end

// 数据锁存和编码控制
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        encode_data   <= 8'h00;
        encode_enable <= 1'b0;
        rd_state      <= 2'b00;  // 初始运行差异为0
    end else begin
        case (current_state)
            IDLE: begin
                encode_enable <= 1'b0;
                if (s_axis_tvalid && s_axis_tready) begin
                    // 锁存输入数据并启动编码
                    encode_data   <= s_axis_tdata;
                    encode_enable <= 1'b1;
                end
            end
            
            ENCODE: begin
                // 保持编码使能，准备接收编码结果
                encode_enable <= 1'b1;
            end
            
            TRANSMIT: begin
                encode_enable <= 1'b0;
                if (bit_counter == 4'd9) begin
                    // 传输完成，更新运行差异状态
                    rd_state <= rd_out;
                end
            end
            
            default: begin
                encode_enable <= 1'b0;
            end
        endcase
    end
end

// 并串转换和LVDS输出控制
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        shift_register <= 10'b0;
        bit_counter    <= 4'b0;
        lvds_data      <= 1'b0;
    end else begin
        case (current_state)
            IDLE: begin
                bit_counter <= 4'b0;
                lvds_data   <= 1'b0;
            end
            
            ENCODE: begin
                // 编码完成，加载编码结果到移位寄存器
                shift_register <= encoded_data;
                bit_counter    <= 4'b0;
            end
            
            TRANSMIT: begin
                // 串行输出，从MSB开始
                lvds_data      <= shift_register[9];
                shift_register <= {shift_register[8:0], 1'b0};
                
                if (bit_counter < 4'd9) begin
                    bit_counter <= bit_counter + 1'b1;
                end
            end
            
            default: begin
                lvds_data <= 1'b0;
            end
        endcase
    end
end

endmodule
