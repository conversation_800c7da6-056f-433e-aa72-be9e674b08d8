`timescale 1ns / 1ps

module lvds_8b10b_transmitter (
    // 时钟和复位
    input  wire        clk,           // 系统时钟 (10MHz，用于LVDS输出)
    input  wire        rst_n,         // 复位信号（低电平有效）

    // AXI Stream 输入接口
    input  wire [7:0]  s_axis_tdata,  // 输入数据
    input  wire        s_axis_tvalid, // 输入数据有效
    input  wire        s_axis_tlast,  // 帧结束信号
    output reg         s_axis_tready, // 准备接收数据

    // 控制接口
    input  wire        insert_comma,  // 插入K28.5 Comma码

    // LVDS 串行输出
    output reg         lvds_data      // 单bit串行数据输出 (10MHz)
);

// 状态机定义
localparam [2:0] IDLE        = 3'b000,  // 空闲状态，等待输入数据
                 ENCODE      = 3'b001,  // 编码状态，等待编码器输出
                 TRANSMIT    = 3'b010,  // 传输状态，串行输出数据
                 FRAME_GAP   = 3'b011,  // 帧间隔状态
                 INSERT_COMMA = 3'b100; // 插入K28.5状态

// K28.5 Comma码定义
localparam [7:0] K28_5 = 8'hBC;     // K28.5 = 8'b10111100

// 内部信号定义
reg [2:0]  current_state;           // 当前状态
reg [2:0]  next_state;              // 下一状态

// 编码器相关信号
reg [7:0]  encode_data;             // 送入编码器的数据
reg        encode_enable;           // 编码器使能信号
reg        encode_tk;               // 编码器K字符控制信号
reg [1:0]  rd_state;                // 运行差异状态
wire [9:0] encoded_data;            // 编码器输出的10位数据
wire [1:0] rd_out;                  // 编码器输出的运行差异

// 并串转换相关信号
reg [9:0]  shift_register;          // 10位移位寄存器
reg [3:0]  bit_counter;             // 位计数器 (0-9)

// 帧间隔控制
reg [4:0]  gap_counter;             // 帧间隔计数器 (最少16个时钟周期)
reg        frame_end_flag;          // 帧结束标志

// 实例化8b/10b编码器 (支持K字符编码)
encoder_8b10b u_encoder (
    .data_in(encode_data),           // 8位输入数据
    .rd_in(rd_state),                // 运行差异输入
    .clk(clk),                       // 时钟 (10MHz)
    .rst(rst_n),                     // 复位
    .tk(encode_tk),                  // K字符控制 (1=K字符, 0=数据字符)
    .data_out(encoded_data),         // 10位编码输出
    .rd_out(rd_out),                 // 运行差异输出
    .en_data_in(encode_enable)       // 编码使能
);

// 状态机 - 组合逻辑
always @(*) begin
    case (current_state)
        IDLE: begin
            // 空闲状态：检查是否需要插入Comma码或处理数据
            if (insert_comma) begin
                next_state = INSERT_COMMA;
            end else if (s_axis_tvalid && s_axis_tready) begin
                next_state = ENCODE;
            end else begin
                next_state = IDLE;
            end
        end

        INSERT_COMMA: begin
            // 插入K28.5 Comma码
            next_state = ENCODE;
        end

        ENCODE: begin
            // 编码状态：等待编码器完成（1个时钟周期）
            next_state = TRANSMIT;
        end

        TRANSMIT: begin
            // 传输状态：串行输出10位数据
            if (bit_counter == 4'd9) begin
                if (frame_end_flag) begin
                    next_state = FRAME_GAP;  // 帧结束，进入帧间隔
                end else begin
                    next_state = IDLE;       // 继续处理下一个数据
                end
            end else begin
                next_state = TRANSMIT;
            end
        end

        FRAME_GAP: begin
            // 帧间隔状态：等待至少16个时钟周期
            if (gap_counter >= 5'd15) begin  // 16个时钟周期 (0-15)
                next_state = IDLE;
            end else begin
                next_state = FRAME_GAP;
            end
        end

        default: begin
            next_state = IDLE;
        end
    endcase
end

// 状态机 - 时序逻辑
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        current_state <= IDLE;
    end else begin
        current_state <= next_state;
    end
end

// AXI Stream 握手控制
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        s_axis_tready <= 1'b0;
    end else begin
        // 只有在空闲状态且不需要插入Comma码时才能接收新数据
        s_axis_tready <= (current_state == IDLE) && !insert_comma;
    end
end

// 数据锁存和编码控制
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        encode_data    <= 8'h00;
        encode_enable  <= 1'b0;
        encode_tk      <= 1'b0;
        rd_state       <= 2'b00;  // 初始运行差异为0
        frame_end_flag <= 1'b0;
    end else begin
        case (current_state)
            IDLE: begin
                encode_enable <= 1'b0;
                encode_tk     <= 1'b0;
                frame_end_flag <= 1'b0;

                if (s_axis_tvalid && s_axis_tready) begin
                    // 锁存输入数据并启动编码
                    encode_data    <= s_axis_tdata;
                    encode_enable  <= 1'b1;
                    encode_tk      <= 1'b0;  // 数据字符
                    frame_end_flag <= s_axis_tlast;  // 记录帧结束标志
                end
            end

            INSERT_COMMA: begin
                // 插入K28.5 Comma码
                encode_data   <= K28_5;
                encode_enable <= 1'b1;
                encode_tk     <= 1'b1;   // K字符
            end

            ENCODE: begin
                // 保持编码使能，准备接收编码结果
                encode_enable <= 1'b1;
            end

            TRANSMIT: begin
                encode_enable <= 1'b0;
                if (bit_counter == 4'd9) begin
                    // 传输完成，更新运行差异状态
                    rd_state <= rd_out;
                end
            end

            FRAME_GAP: begin
                encode_enable <= 1'b0;
            end

            default: begin
                encode_enable <= 1'b0;
            end
        endcase
    end
end

// 并串转换和LVDS输出控制
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        shift_register <= 10'b0;
        bit_counter    <= 4'b0;
        gap_counter    <= 5'b0;
        lvds_data      <= 1'b0;
    end else begin
        case (current_state)
            IDLE: begin
                bit_counter <= 4'b0;
                gap_counter <= 5'b0;
                lvds_data   <= 1'b0;  // 空闲时输出0
            end

            INSERT_COMMA: begin
                bit_counter <= 4'b0;
            end

            ENCODE: begin
                // 编码完成，加载编码结果到移位寄存器
                shift_register <= encoded_data;
                bit_counter    <= 4'b0;
            end

            TRANSMIT: begin
                // 串行输出，从MSB开始 (10MHz速率)
                lvds_data      <= shift_register[9];
                shift_register <= {shift_register[8:0], 1'b0};

                if (bit_counter < 4'd9) begin
                    bit_counter <= bit_counter + 1'b1;
                end
            end

            FRAME_GAP: begin
                // 帧间隔期间输出0，计数至少16个时钟周期
                lvds_data <= 1'b0;
                if (gap_counter < 5'd15) begin
                    gap_counter <= gap_counter + 1'b1;
                end else begin
                    gap_counter <= 5'b0;  // 重置计数器
                end
            end

            default: begin
                lvds_data <= 1'b0;
            end
        endcase
    end
end

endmodule
