`timescale 1ns / 1ps

module lvds_8b10b_transmitter (
    // 时钟和复位
    input  wire        clk,           // 系统时钟 (10MHz，用于LVDS输出)
    input  wire        rst_n,         // 复位信号（低电平有效）

    // AXI Stream 输入接口
    input  wire [7:0]  s_axis_tdata,  // 输入数据
    input  wire        s_axis_tvalid, // 输入数据有效
    input  wire        s_axis_tlast,  // 帧结束信号
    output reg         s_axis_tready, // 准备接收数据

    // 控制接口 - 移除insert_comma，改为自动管理

    // LVDS 串行输出
    output reg         lvds_data      // 单bit串行数据输出 (10MHz)
);

// 状态机定义
localparam [2:0] IDLE_COMMA  = 3'b000,  // 空闲状态，连续发送K28.5
                 DATA_ENCODE = 3'b001,  // 数据编码状态
                 DATA_TRANSMIT = 3'b010, // 数据传输状态
                 FRAME_GAP   = 3'b011;  // 帧间隔状态（发送K28.5）

// K28.5 Comma码定义
localparam [7:0] K28_5 = 8'hBC;     // K28.5 = 8'b10111100

// 内部信号定义
reg [2:0]  current_state;           // 当前状态
reg [2:0]  next_state;              // 下一状态

// 编码器相关信号
reg [7:0]  encode_data;             // 送入编码器的数据
reg        encode_enable;           // 编码器使能信号
reg        encode_tk;               // 编码器K字符控制信号
reg [1:0]  rd_state;                // 运行差异状态
wire [9:0] encoded_data;            // 编码器输出的10位数据
wire [1:0] rd_out;                  // 编码器输出的运行差异

// 并串转换相关信号
reg [9:0]  shift_register;          // 10位移位寄存器
reg [3:0]  bit_counter;             // 位计数器 (0-9)

// 帧间隔控制
reg [4:0]  gap_counter;             // 帧间隔计数器 (最少16个时钟周期)
reg        frame_end_flag;          // 帧结束标志

// 实例化8b/10b编码器 (支持K字符编码)
encoder_8b10b u_encoder (
    .data_in(encode_data),           // 8位输入数据
    .rd_in(rd_state),                // 运行差异输入
    .clk(clk),                       // 时钟 (10MHz)
    .rst(rst_n),                     // 复位
    .tk(encode_tk),                  // K字符控制 (1=K字符, 0=数据字符)
    .data_out(encoded_data),         // 10位编码输出
    .rd_out(rd_out),                 // 运行差异输出
    .en_data_in(encode_enable)       // 编码使能
);

// 状态机 - 组合逻辑
always @(*) begin
    case (current_state)
        IDLE_COMMA: begin
            // 空闲状态：连续发送K28.5，直到有数据需要传输
            if (s_axis_tvalid && s_axis_tready) begin
                next_state = DATA_ENCODE;  // 有数据时切换到数据编码
            end else begin
                next_state = IDLE_COMMA;   // 继续发送K28.5
            end
        end

        DATA_ENCODE: begin
            // 数据编码状态：等待编码器完成（1个时钟周期）
            next_state = DATA_TRANSMIT;
        end

        DATA_TRANSMIT: begin
            // 数据传输状态：串行输出10位数据
            if (bit_counter == 4'd9) begin
                if (frame_end_flag) begin
                    next_state = FRAME_GAP;    // 帧结束，进入帧间隔
                end else if (s_axis_tvalid) begin
                    next_state = DATA_ENCODE;  // 还有数据，继续编码
                end else begin
                    next_state = IDLE_COMMA;   // 无数据，恢复发送K28.5
                end
            end else begin
                next_state = DATA_TRANSMIT;
            end
        end

        FRAME_GAP: begin
            // 帧间隔状态：发送K28.5并等待至少16个时钟周期
            if (gap_counter >= 5'd15) begin  // 16个时钟周期 (0-15)
                next_state = IDLE_COMMA;     // 返回空闲状态继续发送K28.5
            end else begin
                next_state = FRAME_GAP;
            end
        end

        default: begin
            next_state = IDLE_COMMA;
        end
    endcase
end

// 状态机 - 时序逻辑
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        current_state <= IDLE;
    end else begin
        current_state <= next_state;
    end
end

// AXI Stream 握手控制
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        s_axis_tready <= 1'b0;
    end else begin
        // 在空闲发送K28.5状态或数据传输完成时可以接收新数据
        s_axis_tready <= (current_state == IDLE_COMMA) ||
                        (current_state == DATA_TRANSMIT && bit_counter == 4'd9 && !frame_end_flag);
    end
end

// 数据锁存和编码控制
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        encode_data    <= K28_5;  // 复位后立即准备发送K28.5
        encode_enable  <= 1'b1;   // 复位后立即开始编码
        encode_tk      <= 1'b1;   // K字符
        rd_state       <= 2'b00;  // 初始运行差异为0
        frame_end_flag <= 1'b0;
    end else begin
        case (current_state)
            IDLE_COMMA: begin
                // 空闲状态：连续编码K28.5
                encode_data   <= K28_5;
                encode_enable <= 1'b1;
                encode_tk     <= 1'b1;   // K字符
                frame_end_flag <= 1'b0;

                if (s_axis_tvalid && s_axis_tready) begin
                    // 有数据时，准备切换到数据编码
                    encode_data    <= s_axis_tdata;
                    encode_tk      <= 1'b0;  // 数据字符
                    frame_end_flag <= s_axis_tlast;
                end
            end

            DATA_ENCODE: begin
                // 数据编码状态：保持编码使能
                encode_enable <= 1'b1;
            end

            DATA_TRANSMIT: begin
                if (bit_counter == 4'd9) begin
                    // 当前数据传输完成，更新运行差异状态
                    rd_state <= rd_out;

                    if (!frame_end_flag && s_axis_tvalid) begin
                        // 还有数据，准备编码下一个数据
                        encode_data    <= s_axis_tdata;
                        encode_enable  <= 1'b1;
                        encode_tk      <= 1'b0;
                        frame_end_flag <= s_axis_tlast;
                    end else if (!frame_end_flag) begin
                        // 无更多数据，恢复发送K28.5
                        encode_data   <= K28_5;
                        encode_enable <= 1'b1;
                        encode_tk     <= 1'b1;
                    end else begin
                        // 帧结束，停止编码等待帧间隔
                        encode_enable <= 1'b0;
                    end
                end else begin
                    encode_enable <= 1'b0;  // 传输期间停止编码
                end
            end

            FRAME_GAP: begin
                // 帧间隔期间发送K28.5
                encode_data   <= K28_5;
                encode_enable <= 1'b1;
                encode_tk     <= 1'b1;
            end

            default: begin
                encode_data   <= K28_5;
                encode_enable <= 1'b1;
                encode_tk     <= 1'b1;
            end
        endcase
    end
end

// 并串转换和LVDS输出控制
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        shift_register <= 10'b0;
        bit_counter    <= 4'b0;
        gap_counter    <= 5'b0;
        lvds_data      <= 1'b0;
    end else begin
        case (current_state)
            IDLE_COMMA: begin
                // 空闲状态：连续传输K28.5编码结果
                if (bit_counter == 4'd0) begin
                    // 加载新的K28.5编码结果
                    shift_register <= encoded_data;
                end

                // 串行输出K28.5
                lvds_data      <= shift_register[9];
                shift_register <= {shift_register[8:0], 1'b0};

                if (bit_counter < 4'd9) begin
                    bit_counter <= bit_counter + 1'b1;
                end else begin
                    bit_counter <= 4'b0;  // 重新开始下一个K28.5
                end

                gap_counter <= 5'b0;
            end

            DATA_ENCODE: begin
                // 数据编码完成，加载编码结果到移位寄存器
                shift_register <= encoded_data;
                bit_counter    <= 4'b0;
            end

            DATA_TRANSMIT: begin
                // 串行输出数据，从MSB开始 (10MHz速率)
                lvds_data      <= shift_register[9];
                shift_register <= {shift_register[8:0], 1'b0};

                if (bit_counter < 4'd9) begin
                    bit_counter <= bit_counter + 1'b1;
                end
            end

            FRAME_GAP: begin
                // 帧间隔期间继续发送K28.5，并计数至少16个时钟周期
                if (bit_counter == 4'd0) begin
                    shift_register <= encoded_data;  // 加载K28.5编码
                end

                lvds_data      <= shift_register[9];
                shift_register <= {shift_register[8:0], 1'b0};

                if (bit_counter < 4'd9) begin
                    bit_counter <= bit_counter + 1'b1;
                end else begin
                    bit_counter <= 4'b0;

                    // 帧间隔计数
                    if (gap_counter < 5'd15) begin
                        gap_counter <= gap_counter + 1'b1;
                    end else begin
                        gap_counter <= 5'b0;  // 重置计数器
                    end
                end
            end

            default: begin
                lvds_data <= 1'b0;
            end
        endcase
    end
end

endmodule
