`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date: 2025/01/23
// Design Name: 
// Module Name: tb_lvds_8b10b_transmitter
// Project Name: 
// Target Devices: 
// Tool Versions: 
// Description: lvds_8b10b_transmitter模块的测试平台
// 
// Dependencies: lvds_8b10b_transmitter.v, encoder.v
// 
// Revision:
// Revision 0.01 - File Created
// Additional Comments:
// 
//////////////////////////////////////////////////////////////////////////////////

module tb_lvds_8b10b_transmitter;

// 测试信号定义
reg         clk;
reg         rst_n;
reg [7:0]   s_axis_tdata;
reg         s_axis_tvalid;
wire        s_axis_tready;
wire        lvds_data;

// 测试数据数组
reg [7:0] test_data [0:7];
integer   data_index;

// 实例化被测模块
lvds_8b10b_transmitter uut (
    .clk(clk),
    .rst_n(rst_n),
    .s_axis_tdata(s_axis_tdata),
    .s_axis_tvalid(s_axis_tvalid),
    .s_axis_tready(s_axis_tready),
    .lvds_data(lvds_data)
);

// 时钟生成 - 50MHz
initial begin
    clk = 0;
    forever #10 clk = ~clk;  // 20ns周期 = 50MHz
end

// 测试数据初始化
initial begin
    test_data[0] = 8'h00;  // 测试数据0
    test_data[1] = 8'h55;  // 测试数据1 (01010101)
    test_data[2] = 8'hAA;  // 测试数据2 (10101010)
    test_data[3] = 8'hFF;  // 测试数据3 (11111111)
    test_data[4] = 8'h0F;  // 测试数据4 (00001111)
    test_data[5] = 8'hF0;  // 测试数据5 (11110000)
    test_data[6] = 8'h3C;  // 测试数据6 (00111100)
    test_data[7] = 8'hC3;  // 测试数据7 (11000011)
end

// 主测试流程
initial begin
    // 初始化信号
    rst_n = 0;
    s_axis_tdata = 8'h00;
    s_axis_tvalid = 0;
    data_index = 0;
    
    // 显示测试开始信息
    $display("========================================");
    $display("8b/10b LVDS传输器测试开始");
    $display("时钟频率: 50MHz");
    $display("========================================");
    
    // 复位序列
    #100;
    rst_n = 1;
    $display("时间 %0t: 复位释放", $time);
    
    // 等待几个时钟周期
    #200;
    
    // 发送测试数据
    for (data_index = 0; data_index < 8; data_index = data_index + 1) begin
        send_data(test_data[data_index]);
        #50; // 数据间隔
    end
    
    // 测试连续发送（无间隔）
    $display("\n开始连续发送测试...");
    for (data_index = 0; data_index < 4; data_index = data_index + 1) begin
        send_data_continuous(test_data[data_index]);
    end
    
    // 等待最后一个数据传输完成
    #500;
    
    $display("\n========================================");
    $display("测试完成");
    $display("========================================");
    $finish;
end

// 发送单个数据的任务（带间隔）
task send_data;
    input [7:0] data;
    begin
        $display("时间 %0t: 发送数据 0x%02h (%08b)", $time, data, data);
        
        // 等待ready信号
        wait(s_axis_tready);
        @(posedge clk);
        
        // 发送数据
        s_axis_tdata = data;
        s_axis_tvalid = 1;
        
        // 等待握手完成
        @(posedge clk);
        while (!(s_axis_tvalid && s_axis_tready)) begin
            @(posedge clk);
        end
        
        // 撤销valid信号
        s_axis_tvalid = 0;
        
        // 等待传输完成（大约11个时钟周期）
        #240;
        $display("时间 %0t: 数据 0x%02h 传输完成", $time, data);
    end
endtask

// 连续发送数据的任务（无间隔）
task send_data_continuous;
    input [7:0] data;
    begin
        $display("时间 %0t: 连续发送数据 0x%02h", $time, data);
        
        // 等待ready信号
        wait(s_axis_tready);
        @(posedge clk);
        
        // 发送数据
        s_axis_tdata = data;
        s_axis_tvalid = 1;
        
        // 等待握手完成
        @(posedge clk);
        while (!(s_axis_tvalid && s_axis_tready)) begin
            @(posedge clk);
        end
        
        // 撤销valid信号
        s_axis_tvalid = 0;
    end
endtask

// 监控LVDS输出
always @(posedge clk) begin
    if (rst_n && uut.current_state == 2'b10) begin // TRANSMIT状态
        $display("时间 %0t: LVDS输出 = %b (bit %0d)", $time, lvds_data, uut.bit_counter);
    end
end

// 监控状态变化
always @(posedge clk) begin
    if (rst_n) begin
        case (uut.current_state)
            2'b00: ; // IDLE - 不显示，避免输出过多
            2'b01: $display("时间 %0t: 进入编码状态", $time);
            2'b10: if (uut.bit_counter == 0) 
                       $display("时间 %0t: 开始串行传输，编码结果: %010b", $time, uut.shift_register);
        endcase
    end
end

endmodule
