# 8b/10b LVDS传输器使用说明

## 概述

本项目实现了一个基于8b/10b编码的LVDS串行数据传输器，具有以下特点：

- 使用AXI Stream握手协议
- 输入8位并行数据，输出单bit串行数据
- 直接调用现有的encoder.v模块
- 代码简洁直观，包含详细中文注释
- 自动管理运行差异(RD)状态

## 文件结构

```
├── lvds_8b10b_transmitter.v    # 主传输器模块
├── tb_lvds_8b10b_transmitter.v # 测试平台
├── example_usage.v             # 使用示例
└── README.md                   # 本说明文档
```

## 模块接口

### lvds_8b10b_transmitter

```verilog
module lvds_8b10b_transmitter (
    // 时钟和复位
    input  wire        clk,           // 系统时钟
    input  wire        rst_n,         // 复位信号（低电平有效）
    
    // AXI Stream 输入接口
    input  wire [7:0]  s_axis_tdata,  // 输入数据
    input  wire        s_axis_tvalid, // 输入数据有效
    output reg         s_axis_tready, // 准备接收数据
    
    // LVDS 串行输出
    output reg         lvds_data      // 单bit串行数据输出
);
```

## 工作原理

### 1. 状态机设计

传输器采用3状态状态机：

- **IDLE**: 空闲状态，等待输入数据
- **ENCODE**: 编码状态，数据送入encoder.v进行8b/10b编码
- **TRANSMIT**: 传输状态，将10位编码数据串行输出

### 2. 时序特性

- **编码延迟**: 1个时钟周期
- **传输时间**: 10个时钟周期（每个编码字符）
- **总延迟**: 11个时钟周期/字节
- **数据效率**: 80% (8bit输入 -> 10bit输出)

### 3. AXI Stream握手

```
时钟周期:  1    2    3    4    5    6    7    8    9   10   11   12
tvalid  : _____|‾‾‾‾‾|_________________________________________
tready  : ‾‾‾‾‾|_____|‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾‾
状态    : IDLE |ENCODE|        TRANSMIT (10个时钟周期)        |IDLE
```

## 使用方法

### 1. 基本使用

```verilog
// 实例化传输器
lvds_8b10b_transmitter u_tx (
    .clk(clk_50m),
    .rst_n(rst_n),
    .s_axis_tdata(data_to_send),
    .s_axis_tvalid(data_valid),
    .s_axis_tready(ready_for_data),
    .lvds_data(serial_output)
);

// 发送数据
always @(posedge clk) begin
    if (ready_for_data && have_data_to_send) begin
        data_to_send <= next_data;
        data_valid <= 1'b1;
    end else begin
        data_valid <= 1'b0;
    end
end
```

### 2. 连续数据传输

参考 `example_usage.v` 中的 `advanced_usage_with_fifo` 模块，展示了如何：

- 使用FIFO缓存突发数据
- 实现连续无间隔传输
- 自动控制传输流程

## 仿真测试

### 运行测试

```bash
# 使用ModelSim/QuestaSim
vsim -do "run -all" tb_lvds_8b10b_transmitter

# 或使用Vivado
# 添加所有.v文件到项目，设置tb_lvds_8b10b_transmitter为顶层
```

### 测试内容

测试平台包含以下测试用例：

1. **基本功能测试**: 发送8个不同的测试数据
2. **连续传输测试**: 无间隔连续发送数据
3. **握手协议测试**: 验证AXI Stream握手时序
4. **状态监控**: 实时显示状态机状态和LVDS输出

### 预期结果

```
========================================
8b/10b LVDS传输器测试开始
时钟频率: 50MHz
========================================
时间 100: 复位释放
时间 300: 发送数据 0x00 (00000000)
时间 320: 进入编码状态
时间 340: 开始串行传输，编码结果: 0100111011
时间 340: LVDS输出 = 0 (bit 0)
时间 360: LVDS输出 = 1 (bit 1)
...
时间 540: 数据 0x00 传输完成
```

## 性能参数

| 参数 | 值 |
|------|-----|
| 最大时钟频率 | 取决于encoder.v模块 |
| 数据吞吐率 | 时钟频率 × 8/11 |
| 延迟 | 11个时钟周期 |
| 资源占用 | 约100个LUT + 50个FF |

## 注意事项

1. **时钟域**: 确保所有信号都在同一时钟域
2. **复位**: 使用异步复位，同步释放
3. **LVDS驱动**: 在实际FPGA中使用专用LVDS输出原语
4. **背压处理**: 当tready为低时，不要改变tdata
5. **运行差异**: 模块自动管理RD状态，无需外部干预

## 扩展功能

可以在此基础上添加：

- K字符插入（控制字符）
- 错误检测和纠正
- 多通道并行传输
- 动态时钟切换
- 功耗优化模式

## 依赖模块

- `encoder.v`: 8b/10b编码器（需要包含在项目中）
- `encoder_5b.v`: 5b编码器（encoder.v的子模块）
- `encoder_3b.v`: 3b编码器（encoder.v的子模块）
