# PMU LVDS传输器技术规格实现

## 📋 需求对照表

| 需求项目 | 规格要求 | 实现方案 | 状态 |
|----------|----------|----------|------|
| 传输方式 | 单线制LVDS | lvds_data输出 | ✅ |
| 数据用途 | 发送科学数据至PMU | AXI Stream接口 | ✅ |
| 编码方式 | 8b/10b编码 | encoder_8b10b模块 | ✅ |
| 传输速率 | 10MHz | 10MHz时钟驱动 | ✅ |
| 同步码 | K28.5 Comma码 | insert_comma控制 | ✅ |
| 帧间隔 | ≥16个时钟周期 | FRAME_GAP状态 | ✅ |
| 时钟误差 | ±3% | 支持9.7-10.3MHz | ✅ |

## 🔧 技术实现详解

### 1. LVDS速率控制 (10MHz)

```verilog
// 系统时钟必须为10MHz
input wire clk,  // 10MHz时钟输入

// LVDS输出每个时钟周期输出1位
always @(posedge clk) begin
    lvds_data <= shift_register[9];  // 10MHz位速率
end
```

**关键参数计算：**
- LVDS位速率: 10 Mbps
- 有效数据速率: 10 × (8/11) = 7.27 Mbps  
- 字节吞吐率: 10MHz ÷ 11 = 909 KB/s

### 2. K28.5 Comma码实现

```verilog
// K28.5定义
localparam [7:0] K28_5 = 8'hBC;  // 10111100

// 空闲状态连续发送K28.5
IDLE_COMMA: begin
    encode_data <= K28_5;
    encode_tk   <= 1'b1;    // K字符标志
    encode_enable <= 1'b1;  // 持续编码
end
```

**正确的使用方式：**
- **空闲时连续发送**: 无数据传输时持续发送K28.5
- **数据传输时停止**: 有数据时自动切换到数据传输
- **传输完成后恢复**: 数据传输完成立即恢复K28.5发送
- **帧间隔期间继续**: 帧间隔期间也发送K28.5而非空闲

**作用：**
- 字节对齐和同步
- 保持链路活跃
- 时钟恢复辅助

### 3. 帧间隔控制 (≥16时钟周期)

```verilog
// 帧间隔状态机
FRAME_GAP: begin
    lvds_data <= 1'b0;  // 间隔期间输出0
    if (gap_counter >= 5'd15) begin  // 16个时钟周期
        next_state = IDLE;
    end else begin
        gap_counter <= gap_counter + 1'b1;
    end
end
```

**时序保证：**
- 当 `s_axis_tlast = 1` 时触发帧结束
- 自动进入16个时钟周期的间隔期
- 间隔期间LVDS输出保持低电平

### 4. 时钟频率容差 (±3%)

**支持频率范围：**
- 最低频率: 9.7MHz (103.09ns周期)
- 标准频率: 10.0MHz (100.00ns周期)  
- 最高频率: 10.3MHz (97.09ns周期)

**实现特点：**
- 所有时序逻辑基于时钟边沿
- 无硬编码延迟，自适应频率变化
- 帧间隔始终保持≥16个时钟周期

## 🚀 使用示例

### 基本使用流程

```verilog
// 1. 实例化传输器
lvds_8b10b_transmitter pmu_transmitter (
    .clk(clk_10mhz),              // 10MHz系统时钟
    .rst_n(rst_n),
    .s_axis_tdata(science_data),   // 科学数据
    .s_axis_tvalid(data_valid),
    .s_axis_tlast(frame_end),      // 帧结束标志
    .s_axis_tready(ready_for_data),
    .lvds_data(lvds_to_pmu)       // 发送至PMU
);

// 2. 发送数据序列
initial begin
    // 复位后自动开始连续发送K28.5
    rst_n = 0;
    #100;
    rst_n = 1;  // K28.5连续发送开始

    // 等待需要发送数据时
    #1000;

    // 发送科学数据包 (K28.5自动停止)
    for (i = 0; i < packet_length; i++) begin
        wait(ready_for_data);
        science_data = data_array[i];
        data_valid = 1;
        frame_end = (i == packet_length-1);
        @(posedge clk_10mhz);
        data_valid = 0;
        frame_end = 0;
    end

    // 数据发送完成后自动恢复K28.5发送
    // 帧间隔期间也继续发送K28.5
end
```

### 科学数据包格式建议

```verilog
// 典型科学数据包结构
typedef struct {
    logic [7:0] sync_pattern;    // 0xBC (K28.5已单独发送)
    logic [7:0] packet_header;   // 包头标识
    logic [7:0] sequence_num;    // 序列号
    logic [7:0] data_length;     // 数据长度
    logic [7:0] science_data[N]; // 科学数据载荷
    logic [15:0] checksum;       // 校验和
    logic [7:0] packet_tail;     // 包尾标识
} science_packet_t;
```

## 📊 性能指标

### 传输效率分析

| 参数 | 数值 | 说明 |
|------|------|------|
| LVDS位速率 | 10 Mbps | 固定10MHz时钟 |
| 8b/10b效率 | 80% | 编码开销 |
| 有效数据速率 | 7.27 Mbps | 考虑编码开销 |
| 字节吞吐率 | 909 KB/s | 实际数据传输率 |
| 帧间隔开销 | 1.6μs | 16×100ns |

### 延迟特性

| 阶段 | 延迟 | 说明 |
|------|------|------|
| 编码延迟 | 100ns | 1个时钟周期 |
| 传输延迟 | 1000ns | 10个时钟周期 |
| 总延迟 | 1100ns | 每字节处理时间 |
| 帧间隔 | ≥1600ns | 最少16个时钟周期 |

## 🔍 测试验证

### 仿真测试

```bash
# 运行PMU规格测试
vsim -do "run -all" tb_pmu_lvds_transmitter

# 预期输出
PMU LVDS传输器测试开始
LVDS速率: 10MHz
时间 500: 复位释放
时间 1500: 发送K28.5 Comma码
时间 2700: K28.5 Comma码传输完成
时间 2800: 开始发送科学数据帧
...
时间 25000: 帧间隔完成
所有测试用例通过
```

### 硬件验证要点

1. **时钟质量检查**
   - 使用示波器测量10MHz时钟
   - 验证频率稳定性在±3%范围内
   - 检查时钟抖动和占空比

2. **LVDS信号质量**
   - 差分电压幅度: 350mV典型值
   - 共模电压: 1.2V典型值
   - 上升/下降时间: <1ns

3. **数据完整性验证**
   - 使用逻辑分析仪捕获LVDS数据
   - 验证K28.5 Comma码正确性
   - 检查帧间隔时序

## ⚠️ 注意事项

1. **时钟域设计**
   - 确保所有信号在10MHz时钟域内
   - 避免跨时钟域的信号传递

2. **LVDS驱动器选择**
   - 使用FPGA专用LVDS输出原语
   - 配置合适的驱动强度和终端匹配

3. **EMI/EMC考虑**
   - 10MHz基频及其谐波的辐射控制
   - 差分走线设计和屏蔽

4. **功耗优化**
   - 在不传输时可考虑时钟门控
   - 优化LVDS驱动器功耗设置

## 📈 扩展功能

可在基础功能上添加：

1. **错误检测**
   - CRC校验码生成
   - 传输错误统计

2. **流控制**
   - 背压处理机制
   - 缓冲区管理

3. **诊断功能**
   - 传输状态监控
   - 性能计数器

4. **多通道支持**
   - 并行多路LVDS输出
   - 通道间同步控制
