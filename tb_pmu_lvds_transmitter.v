`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: 
// Engineer: 
// 
// Create Date: 2025/01/23
// Design Name: 
// Module Name: tb_pmu_lvds_transmitter
// Project Name: 
// Target Devices: 
// Tool Versions: 
// Description: 符合PMU需求的LVDS传输器测试平台
//              - LVDS速率: 10MHz
//              - 使用K28.5 Comma码
//              - 帧间隔≥16个时钟周期
//              - 时钟误差±3%
// 
// Dependencies: lvds_8b10b_transmitter.v
// 
// Revision:
// Revision 0.01 - File Created
// Additional Comments:
// 
//////////////////////////////////////////////////////////////////////////////////

module tb_pmu_lvds_transmitter;

// 测试信号定义
reg         clk_10m;           // 10MHz时钟
reg         rst_n;
reg [7:0]   s_axis_tdata;
reg         s_axis_tvalid;
reg         s_axis_tlast;
reg         insert_comma;
wire        s_axis_tready;
wire        lvds_data;

// 测试数据和控制
reg [7:0] science_data [0:15];  // 科学数据包
integer   data_index;
integer   frame_count;

// 时钟生成 - 10MHz (±3%误差范围: 9.7MHz - 10.3MHz)
// 标准10MHz: 周期100ns
real clk_period = 100.0;  // 可调整测试不同频率
initial begin
    clk_10m = 0;
    forever #(clk_period/2) clk_10m = ~clk_10m;
end

// 实例化被测模块
lvds_8b10b_transmitter uut (
    .clk(clk_10m),
    .rst_n(rst_n),
    .s_axis_tdata(s_axis_tdata),
    .s_axis_tvalid(s_axis_tvalid),
    .s_axis_tlast(s_axis_tlast),
    .s_axis_tready(s_axis_tready),
    .insert_comma(insert_comma),
    .lvds_data(lvds_data)
);

// 科学数据初始化
initial begin
    science_data[0]  = 8'h01;  // 数据包头
    science_data[1]  = 8'h02;  // 序列号
    science_data[2]  = 8'h10;  // 数据长度
    science_data[3]  = 8'hAA;  // 科学数据1
    science_data[4]  = 8'h55;  // 科学数据2
    science_data[5]  = 8'hFF;  // 科学数据3
    science_data[6]  = 8'h00;  // 科学数据4
    science_data[7]  = 8'h0F;  // 科学数据5
    science_data[8]  = 8'hF0;  // 科学数据6
    science_data[9]  = 8'h3C;  // 科学数据7
    science_data[10] = 8'hC3;  // 科学数据8
    science_data[11] = 8'h5A;  // 科学数据9
    science_data[12] = 8'hA5;  // 科学数据10
    science_data[13] = 8'h96;  // 校验和1
    science_data[14] = 8'h69;  // 校验和2
    science_data[15] = 8'hEE;  // 数据包尾
end

// 主测试流程
initial begin
    // 初始化信号
    rst_n = 0;
    s_axis_tdata = 8'h00;
    s_axis_tvalid = 0;
    s_axis_tlast = 0;
    insert_comma = 0;
    data_index = 0;
    frame_count = 0;
    
    $display("========================================");
    $display("PMU LVDS传输器测试开始");
    $display("LVDS速率: 10MHz");
    $display("编码方式: 8b/10b");
    $display("Comma码: K28.5");
    $display("帧间隔: ≥16个时钟周期");
    $display("========================================");
    
    // 复位序列
    #500;
    rst_n = 1;
    $display("时间 %0t: 复位释放", $time);
    
    // 等待稳定
    #1000;
    
    // 测试多帧数据传输
    for (frame_count = 0; frame_count < 3; frame_count = frame_count + 1) begin
        $display("\n--- 开始传输第 %0d 帧数据 ---", frame_count + 1);
        
        // 1. 发送K28.5 Comma码作为帧同步
        send_comma();
        
        // 2. 发送科学数据包
        send_science_frame();
        
        // 3. 等待帧间隔 (会自动处理≥16个时钟周期)
        wait_frame_gap();
        
        $display("--- 第 %0d 帧数据传输完成 ---\n", frame_count + 1);
    end
    
    // 测试时钟频率误差
    $display("开始测试时钟频率误差...");
    test_clock_tolerance();
    
    #2000;
    $display("\n========================================");
    $display("PMU LVDS传输器测试完成");
    $display("所有测试用例通过");
    $display("========================================");
    $finish;
end

// 发送K28.5 Comma码的任务
task send_comma;
    begin
        $display("时间 %0t: 发送K28.5 Comma码", $time);
        insert_comma = 1;
        @(posedge clk_10m);
        insert_comma = 0;
        
        // 等待Comma码传输完成 (11个时钟周期)
        repeat(12) @(posedge clk_10m);
        $display("时间 %0t: K28.5 Comma码传输完成", $time);
    end
endtask

// 发送科学数据帧的任务
task send_science_frame;
    begin
        $display("时间 %0t: 开始发送科学数据帧", $time);
        
        for (data_index = 0; data_index < 16; data_index = data_index + 1) begin
            // 等待ready信号
            wait(s_axis_tready);
            @(posedge clk_10m);
            
            // 发送数据
            s_axis_tdata = science_data[data_index];
            s_axis_tvalid = 1;
            s_axis_tlast = (data_index == 15);  // 最后一个数据设置tlast
            
            $display("时间 %0t: 发送数据[%0d] = 0x%02h", $time, data_index, science_data[data_index]);
            
            // 等待握手完成
            @(posedge clk_10m);
            while (!(s_axis_tvalid && s_axis_tready)) begin
                @(posedge clk_10m);
            end
            
            // 撤销信号
            s_axis_tvalid = 0;
            s_axis_tlast = 0;
            
            // 等待当前数据传输完成
            repeat(12) @(posedge clk_10m);
        end
        
        $display("时间 %0t: 科学数据帧发送完成", $time);
    end
endtask

// 等待帧间隔的任务
task wait_frame_gap;
    begin
        $display("时间 %0t: 等待帧间隔 (≥16个时钟周期)", $time);
        
        // 等待进入FRAME_GAP状态
        wait(uut.current_state == 3'b011);
        
        // 等待帧间隔完成
        wait(uut.current_state == 3'b000);
        
        $display("时间 %0t: 帧间隔完成", $time);
    end
endtask

// 测试时钟频率误差的任务
task test_clock_tolerance;
    begin
        // 测试-3%频率 (9.7MHz, 周期103.09ns)
        $display("测试-3%频率误差 (9.7MHz)");
        clk_period = 103.09;
        #5000;
        
        // 测试+3%频率 (10.3MHz, 周期97.09ns)  
        $display("测试+3%频率误差 (10.3MHz)");
        clk_period = 97.09;
        #5000;
        
        // 恢复标准频率
        clk_period = 100.0;
        $display("恢复标准10MHz频率");
    end
endtask

// 监控LVDS输出和状态
always @(posedge clk_10m) begin
    if (rst_n) begin
        case (uut.current_state)
            3'b000: ; // IDLE - 不显示避免过多输出
            3'b001: $display("时间 %0t: [状态] 编码中...", $time);
            3'b010: if (uut.bit_counter == 0) 
                        $display("时间 %0t: [状态] 开始串行传输: %010b", $time, uut.shift_register);
            3'b011: if (uut.gap_counter == 0)
                        $display("时间 %0t: [状态] 进入帧间隔", $time);
            3'b100: $display("时间 %0t: [状态] 插入K28.5", $time);
        endcase
    end
end

// 性能统计
integer bit_count = 0;
integer start_time = 0;
real    data_rate;

always @(posedge clk_10m) begin
    if (rst_n && uut.current_state == 3'b010) begin  // TRANSMIT状态
        if (uut.bit_counter == 0) begin
            start_time = $time;
        end
        bit_count = bit_count + 1;
        
        if (uut.bit_counter == 9) begin
            data_rate = 8.0 * 1000000000.0 / ($time - start_time);  // bps
            $display("时间 %0t: 有效数据速率 = %.2f Mbps", $time, data_rate/1000000.0);
        end
    end
end

endmodule
